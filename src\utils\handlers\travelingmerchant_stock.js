const { EmbedBuilder } = require('discord.js');
const { sendWebhookMessage } = require('../webhookManager');
const path = require('path');
const fs = require('fs');

// Helper function to get allItems.json path
const DEFAULT_ALL_ITEMS_PATH = process.env.ALL_ITEMS_PATH || path.join(__dirname, '../../data/allItems.json');
const FALLBACK_ALL_ITEMS_PATH = path.join(__dirname, '../../allItemsAlterable.json');

function getAllItemsPath() {
    try {
        fs.accessSync(DEFAULT_ALL_ITEMS_PATH);
        return DEFAULT_ALL_ITEMS_PATH;
    } catch {
        return FALLBACK_ALL_ITEMS_PATH;
    }
}

// Cache for allItems.json to avoid repeated file reads
let allItemsCache = null;
let allItemsCacheTime = 0;
const CACHE_DURATION = 60000; // 1 minute cache

// Helper function to get cached allItems data
function getAllItemsData() {
    const now = Date.now();
    if (!allItemsCache || (now - allItemsCacheTime) > CACHE_DURATION) {
        try {
            const allItemsPath = getAllItemsPath();
            allItemsCache = JSON.parse(fs.readFileSync(allItemsPath, 'utf8'));
            allItemsCacheTime = now;
        } catch (error) {
            console.warn('⚠️ Failed to load allItems.json:', error.message);
            allItemsCache = [];
        }
    }
    return allItemsCache;
}

// Helper function to add emoji data to items
function addEmojiData(stockData) {
    try {
        const allItems = getAllItemsData();

        // Create a Map for faster lookups
        const itemMap = new Map();
        for (const item of allItems) {
            itemMap.set(item.name, item);
        }

        return stockData.map(item => {
            const itemData = itemMap.get(item.name);
            return {
                ...item,
                emoji: itemData?.emoji || '❓' // Fallback emoji for merchant items
            };
        });
    } catch (error) {
        console.warn('⚠️ Failed to load emoji data:', error.message);
        return stockData.map(item => ({ ...item, emoji: '💰' }));
    }
}

module.exports = async function handleTravelingMerchantStock({ data, config, roleConfig }) {
    // Use webhook for sending
    const webhookUrl = config.merchantWebhookUrl;
    if (!webhookUrl) return false; // No webhook configured for merchant

    // Check if there's actually merchant stock data
    if (!data.stock || data.stock.length === 0) {
        return false; // No stock data
    }

    // Add emoji data to stock items
    const stockWithEmojis = addEmojiData(data.stock);

    const embed = new EmbedBuilder()
        .setTitle('💰 Merchant Stock Update')
        .setColor('#f1fbd2')
        .setTimestamp()
        .setDescription(stockWithEmojis.map(item => `${item.emoji} **${item.Stock || 0}x** ${item.name}`).join('\n'));

    // Determine merchant type from first item's category using cached data
    let content = '';
    if (roleConfig?.merchantRoles && data.stock && data.stock.length > 0) {
        try {
            const allItems = getAllItemsData();

            // Create a Map for faster lookups
            const itemMap = new Map();
            for (const item of allItems) {
                itemMap.set(item.name, item);
            }

            // Find the first item to determine merchant type
            const firstItem = itemMap.get(data.stock[0].name);
            if (firstItem && firstItem.category && firstItem.category.includes('Merchant')) {
                const merchantType = firstItem.category;
                const roleId = roleConfig.merchantRoles.get(merchantType);
                if (roleId) {
                    content = `<@&${roleId}>`;
                }
            }
        } catch (error) {
            console.warn('⚠️ Failed to determine merchant type:', error.message);
        }
    }

    // Prepare message data
    const messageData = {
        content,
        embeds: [embed]
    };

    try {
        // Send via webhook with invalid webhook detection
        const result = await sendWebhookMessage(webhookUrl, messageData, config.guildId, 'merchantWebhookUrl');
        if (!result.success) {
            if (result.invalidWebhook) {
                console.warn(`⚠️ Invalid merchant webhook removed for guild ${config.guildId}`);
            }
            return false;
        }

        //if (autoReactionConfig?.merchantAutoReaction) {
        //    result.message.react('👍');
        //    result.message.react('👎');
        //}

        return true; // Success
    } catch (error) {
        console.warn(`⚠️ Failed to send merchant stock notification in guild ${config.guildId}: ${error.message}`);
        return false;
    }
};