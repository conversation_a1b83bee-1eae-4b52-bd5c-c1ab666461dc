const { EmbedBuilder } = require('discord.js');
const { sendWebhookMessage } = require('../webhookManager');
const path = require('path');
const fs = require('fs');

// Helper function to get allItems.json path
const DEFAULT_ALL_ITEMS_PATH = process.env.ALL_ITEMS_PATH || path.join(__dirname, '../../data/allItems.json');
const FALLBACK_ALL_ITEMS_PATH = path.join(__dirname, '../../allItemsAlterable.json');

function getAllItemsPath() {
    try {
        fs.accessSync(DEFAULT_ALL_ITEMS_PATH);
        return DEFAULT_ALL_ITEMS_PATH;
    } catch {
        return FALLBACK_ALL_ITEMS_PATH;
    }
}

// This handler now receives combined seed and gear data from the websocket buffer



// Cache for allItems.json to avoid repeated file reads
let allItemsCache = null;
let allItemsCacheTime = 0;
const CACHE_DURATION = 60000; // 1 minute cache

// Helper function to get cached allItems data
function getAllItemsData() {
    const now = Date.now();
    if (!allItemsCache || (now - allItemsCacheTime) > CACHE_DURATION) {
        try {
            const allItemsPath = getAllItemsPath();
            allItemsCache = JSON.parse(fs.readFileSync(allItemsPath, 'utf8'));
            allItemsCacheTime = now;
        } catch (error) {
            console.warn('⚠️ Failed to load allItems.json:', error.message);
            allItemsCache = [];
        }
    }
    return allItemsCache;
}

// Helper function to determine if stock contains seeds or gears and add emoji data
function categorizeStock(stockData) {
    try {
        const allItems = getAllItemsData();

        // Create a Map for faster lookups
        const itemMap = new Map();
        for (const item of allItems) {
            itemMap.set(item.name, item);
        }

        const seeds = [];
        const gears = [];

        for (const item of stockData) {
            const itemData = itemMap.get(item.name);
            if (itemData) {
                // Add emoji data to the item
                const enhancedItem = {
                    ...item,
                    emoji: itemData.emoji || '❓' // Fallback emoji if none defined
                };

                if (itemData.category === 'Fruits') {
                    seeds.push(enhancedItem);
                } else if (itemData.category === 'Gears') {
                    gears.push(enhancedItem);
                }
            }
        }

        return { seeds, gears };
    } catch (error) {
        console.warn('⚠️ Failed to categorize stock items:', error.message);
        return { seeds: [], gears: [] };
    }
}

// Helper function to send combined embed
async function sendCombinedEmbed({ config, roleConfig, seedsData, gearsData }) {
    // Use webhook for sending
    const webhookUrl = config.webhookUrl;
    if (!webhookUrl) return;

    // Build embed fields
    const fields = [];
    const rolePings = [];

    // Add seeds section if we have seed data
    if (seedsData && seedsData.length > 0) {
        fields.push({
            name: '🌱 Seeds',
            value: seedsData.map(item => `${item.emoji} **${item.Stock || 0}x** ${item.name}`).join('\n'),
            inline: false
        });

        // Collect seed role pings
        if (roleConfig?.seedRoles) {
            for (const item of seedsData) {
                if (item.Stock > 0 && roleConfig.seedRoles.has(item.name)) {
                    const roleId = roleConfig.seedRoles.get(item.name);
                    if (!rolePings.includes(roleId)) {
                        rolePings.push(roleId);
                    }
                }
            }
        }
    }

    // Add gears section if we have gear data
    if (gearsData && gearsData.length > 0) {
        fields.push({
            name: '⚙️ Gears',
            value: gearsData.map(item => `${item.emoji} **${item.Stock || 0}x** ${item.name}`).join('\n'),
            inline: false
        });

        // Collect gear role pings
        if (roleConfig?.gearRoles) {
            for (const item of gearsData) {
                if (item.Stock > 0 && roleConfig.gearRoles.has(item.name)) {
                    const roleId = roleConfig.gearRoles.get(item.name);
                    if (!rolePings.includes(roleId)) {
                        rolePings.push(roleId);
                    }
                }
            }
        }
    }

    // Create the embed
    const embed = new EmbedBuilder()
        .setTitle('🔔 Stock Update')
        .setColor('#f1fbd2')
        .setTimestamp()
        .addFields(fields)
        .setFooter({ text: 'discord.gg/K6FArFC25k' });

    // Build content with role pings
    const content = rolePings.length > 0 ? rolePings.map(roleId => `<@&${roleId}>`).join(' ') : '';

    // Prepare message data
    const messageData = {
        content,
        embeds: [embed]
    };

    try {
        // Send via webhook with invalid webhook detection
        const result = await sendWebhookMessage(webhookUrl, messageData, config.guildId, 'webhookUrl');
        if (!result.success) {
            if (result.invalidWebhook) {
                console.warn(`⚠️ Invalid webhook removed for guild ${config.guildId}`);
            }
            return false;
        }

        //if (autoReactionConfig?.seedsGearAutoReaction) {
        //    result.message.react('👍');
        //    result.message.react('👎');
        //}

        return true; // Success
    } catch (error) {
        console.warn(`⚠️ Failed to send combined seed/gear notification in guild ${config.guildId}: ${error.message}`);
        return false;
    }
}

module.exports = async function handleSeedStock({ data, config, roleConfig }) {
    // Use webhook for sending
    const webhookUrl = config.webhookUrl;
    if (!webhookUrl) return false; // No webhook configured

    // Categorize the incoming stock data (which is now combined from the websocket buffer)
    const { seeds, gears } = categorizeStock(data.stock);

    // If this update contains neither seeds nor gears, ignore it
    if (seeds.length === 0 && gears.length === 0) {
        return false; // No relevant stock for this handler
    }

    // Send the combined notification
    const success = await sendCombinedEmbed({
        config,
        roleConfig,
        seedsData: seeds,
        gearsData: gears
    });

    return success;
};