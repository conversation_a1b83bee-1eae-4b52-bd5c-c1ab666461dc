const { EmbedBuilder } = require('discord.js');
const WebSocket = require('ws');
const Bottleneck = require('bottleneck');
const path = require('path');
require('dotenv').config();

const fs = require('fs');
const handlers = {};

// Load handlers dynamically
const handlersPath = path.join(__dirname, 'handlers');
for (const file of fs.readdirSync(handlersPath)) {
    if (file.endsWith('.js')) {
        const type = file.replace('.js', '');
        handlers[type] = require(path.join(handlersPath, file));
    }
}

const StockNotification = require('../models/StockNotification');

// Global buffer for combining seed and gear updates
let seedGearBuffer = { seeds: null, gears: null, timeout: null };
const BUFFER_TIMEOUT = 2000; // 2 seconds to wait for the other update (reduced for speed)

class GardenSocket {
    constructor(url, authToken, client) {
        this.url = url;
        this.authToken = authToken;
        this.client = client;
        this.ws = null;

        // Only shard 0 handles websocket and broadcasting, so it needs full rate limit capacity
        // Discord webhook rate limits are per-webhook, not per-bot, so we can use full capacity
        const totalShards = client.shard?.count || 1;

        // Use full rate limit capacity since only one shard sends notifications
        const requestsPerSecond = 100; // Full capacity for webhook requests
        const maxConcurrent = 100; // High concurrency for parallel webhook sends
        const minTime = 10; // 10ms minimum between requests = 100 req/sec

        console.log(`🔧 Shard ${client.shard?.ids?.[0] || 0}/${totalShards}: Rate limit ${requestsPerSecond} req/sec, ${maxConcurrent} concurrent`);

        this.limiter = new Bottleneck({
            reservoir: requestsPerSecond,
            reservoirRefreshAmount: requestsPerSecond,
            reservoirRefreshInterval: 1000,
            maxConcurrent: maxConcurrent,
            minTime: minTime
        });
    }

    connect(retryDelay = 5000) {
        this.ws = new WebSocket(this.url, {
            headers: {
                Authorization: this.authToken
            }
        });

        this.ws.on('open', () => {
            console.log('🔗 Connected to Grow A Garden WebSocket');
        });

        this.ws.on('message', async (message) => {
            try {
                const formattedMessage = JSON.parse(message);

                this.sendNotification(formattedMessage.type, formattedMessage);
                
                if (formattedMessage.type === 'travelingmerchant_stock') {
                    const logChannel = await this.client.channels.fetch('1384218752765268040');
                    await logChannel.send(`📦 Received traveling merchant stock update:\n\n${JSON.stringify(formattedMessage, null, 2)}`);
                }
            } catch (err) {
                console.error('⚠️ Failed to parse WebSocket message:', err);
            }
        });

        this.ws.on('error', (err) => {
            console.error('⚠️ WebSocket error:', err);
        });

        this.ws.on('close', (code, reason) => {
            console.log(`❌ Disconnected from WebSocket. Code: ${code}, Reason: ${reason}`);
            console.log(`🔁 Attempting to reconnect in ${retryDelay / 1000} seconds...`);

            setTimeout(() => {
                this.connect(retryDelay);
            }, retryDelay);
        });
    }

    async handleSeedGearBuffer(type, data) {
        // Clear existing timeout if any
        if (seedGearBuffer.timeout) {
            clearTimeout(seedGearBuffer.timeout);
            seedGearBuffer.timeout = null;
        }

        // Store the data based on type
        if (type === 'seed_stock') {
            seedGearBuffer.seeds = data;
            console.log(`📦 Received seeds update: ${data.stock?.length || 0} items`);
        } else if (type === 'gear_stock') {
            seedGearBuffer.gears = data;
            console.log(`📦 Received gears update: ${data.stock?.length || 0} items`);
        }

        // Function to send combined notification
        const sendCombinedNotification = async () => {
            // Create combined data structure
            const combinedData = {
                stock: [
                    ...(seedGearBuffer.seeds?.stock || []),
                    ...(seedGearBuffer.gears?.stock || [])
                ]
            };

            console.log(`🚀 Sending combined seed/gear notification to all servers`);

            // Send using the seed_stock handler (which now handles both)
            await this.broadcastToAllServers('seed_stock', combinedData);

            // Reset the buffer
            seedGearBuffer.seeds = null;
            seedGearBuffer.gears = null;
        };

        // Check if we have both seeds and gears
        if (seedGearBuffer.seeds && seedGearBuffer.gears) {
            // We have both, send immediately
            console.log(`🚀 Both seeds and gears received, sending combined notification`);
            await sendCombinedNotification();
        } else {
            // We only have one type, set a timeout to wait for the other
            const hasSeeds = seedGearBuffer.seeds ? 'seeds' : '';
            const hasGears = seedGearBuffer.gears ? 'gears' : '';
            const waiting = hasSeeds ? 'gears' : 'seeds';
            console.log(`⏳ Buffering ${hasSeeds}${hasGears}, waiting ${BUFFER_TIMEOUT}ms for ${waiting}`);

            seedGearBuffer.timeout = setTimeout(async () => {
                // Timeout reached, send whatever we have
                console.log(`⏰ Timeout reached, sending buffered notification`);
                await sendCombinedNotification();
            }, BUFFER_TIMEOUT);
        }
    }

    async sendNotification(type, data) {
        if (!handlers[type]) {
            console.warn(`❌ No handler defined for type "${type}"`);
            return;
        }

        // Special handling for seed_stock and gear_stock to combine them
        if (type === 'seed_stock' || type === 'gear_stock') {
            return this.handleSeedGearBuffer(type, data);
        }

        // For all other types, send normally
        await this.broadcastToAllServers(type, data);
    }

    async broadcastToAllServers(type, data) {
        const startTime = Date.now();
        console.log(`🚀 Starting broadcast of "${type}" to all servers...`);

        this.client.shard.broadcastEval(
            async (client, context) => {
                const path = require('path');
                const StockNotification = require(path.resolve(context.paths.StockNotification));
                const RoleConfig = require(path.resolve(context.paths.RoleConfig));
                const AutoReactionConfig = require(path.resolve(context.paths.AutoReactionConfig));

                const configs = await StockNotification.find({});
                const handler = require(path.resolve(context.handlerPath));

                const Bottleneck = require('bottleneck');
                // Use optimized rate limiting for maximum speed
                // Webhooks have separate rate limits per webhook, so we can be more aggressive
                const limiter = new Bottleneck({
                    reservoir: 100, // 100 requests per second
                    reservoirRefreshAmount: 100,
                    reservoirRefreshInterval: 1000,
                    maxConcurrent: 100, // High concurrency for parallel sends
                    minTime: 10 // 10ms minimum = 100 req/sec max
                });

                // Pre-fetch all role configs and auto-reaction configs in parallel for maximum speed
                // Use Promise.allSettled to handle individual failures gracefully
                const guildIds = configs.map(config => config.guildId);

                const [roleConfigs, autoReactionConfigs] = await Promise.all([
                    Promise.allSettled(guildIds.map(guildId => RoleConfig.findOne({ guildId }))),
                    Promise.allSettled(guildIds.map(guildId => AutoReactionConfig.findOne({ guildId })))
                ]);

                // Create all notification tasks with optimized error handling
                const notificationTasks = [];

                for (let i = 0; i < configs.length; i++) {
                    const config = configs[i];
                    const guild = client.guilds.cache.get(config.guildId);
                    if (!guild) continue; // Skip guilds not on this shard

                    const roleConfigResult = roleConfigs[i];
                    const roleConfig = roleConfigResult.status === 'fulfilled' ? roleConfigResult.value : null;

                    const autoReactionConfigResult = autoReactionConfigs[i];
                    const autoReactionConfig = autoReactionConfigResult.status === 'fulfilled' ? autoReactionConfigResult.value : null;

                    // Create the task with rate limiting
                    const task = limiter.schedule(async () => {
                        try {
                            // Call the handler which will return true/false for success/failure
                            const success = await handler({ client, data: context.data, config, roleConfig, autoReactionConfig });
                            return success ? 1 : 0; // Return 1 for success, 0 for failure/no-send
                        } catch (err) {
                            // Optimized error logging with less overhead
                            const errorCode = err.code;
                            if (errorCode === 50013 || errorCode === 10003 || errorCode === 50001) {
                                // Silent fail for common permission/access errors to reduce log spam
                                return 0;
                            } else {
                                console.warn(`⚠️ Failed to send to guild ${config.guildId}: ${err.message}`);
                                return 0;
                            }
                        }
                    });

                    notificationTasks.push(task);
                }

                // Execute all notifications in parallel with rate limiting
                // Use Promise.allSettled for better error handling and performance
                const results = await Promise.allSettled(notificationTasks);

                // Count successful sends efficiently - handlers now return 1 for success, 0 for failure
                let successCount = 0;
                for (const result of results) {
                    if (result.status === 'fulfilled' && result.value === 1) {
                        successCount++;
                    }
                }

                return successCount;
            },
            {
                context: {
                    data,
                    handlerPath: path.join(__dirname, 'handlers', `${type}.js`),
                    paths: {
                        StockNotification: path.join(__dirname, '../models/StockNotification.js'),
                        RoleConfig: path.join(__dirname, '../models/roleConfig.js'),
                        AutoReactionConfig: path.join(__dirname, '../models/autoReactionConfig.js')
                    }
                }
            }
        )
        .then(results => {
            // Sum up successful sends from all shards
            const total = results.reduce((sum, shardResult) => sum + (shardResult || 0), 0);
            const duration = Date.now() - startTime;
            const rate = total > 0 ? (total / (duration / 1000)).toFixed(1) : '0';
            console.log(`📨 Sent "${type}" update to ${total} guild(s) in ${duration}ms (${rate} guilds/sec)`);
        })
        .catch(err => {
            console.error('❌ Broadcast error:', err);
        });
    }

    close() {
        if (this.ws) this.ws.close();
    }
}

module.exports = GardenSocket;