const { EmbedBuilder } = require('discord.js');
const WebSocket = require('ws');
const Bottleneck = require('bottleneck');
const path = require('path');
require('dotenv').config();

const fs = require('fs');
const handlers = {};

// Load handlers dynamically
const handlersPath = path.join(__dirname, 'handlers');
for (const file of fs.readdirSync(handlersPath)) {
    if (file.endsWith('.js')) {
        const type = file.replace('.js', '');
        handlers[type] = require(path.join(handlersPath, file));
    }
}

const StockNotification = require('../models/StockNotification');

// Global buffer for combining seed and gear updates
let seedGearBuffer = { seeds: null, gears: null, timeout: null };
const BUFFER_TIMEOUT = 2000; // 2 seconds to wait for the other update (reduced for speed)

class GardenSocket {
    constructor(url, authToken, client) {
        this.url = url;
        this.authToken = authToken;
        this.client = client;
        this.ws = null;

        // Calculate per-shard rate limits to avoid exceeding global limits
        const totalShards = client.shard?.count || 1;
        const requestsPerShard = Math.floor(100 / totalShards); // Distribute 100 req/sec across shards
        const concurrentPerShard = Math.floor(50 / totalShards); // Distribute concurrency
        const minTimePerShard = Math.ceil(1000 / requestsPerShard); // Calculate delay

        console.log(`🔧 Shard ${client.shard?.ids?.[0] || 0}/${totalShards}: Rate limit ${requestsPerShard} req/sec, ${concurrentPerShard} concurrent`);

        this.limiter = new Bottleneck({
            reservoir: requestsPerShard,
            reservoirRefreshAmount: requestsPerShard,
            reservoirRefreshInterval: 1000,
            maxConcurrent: concurrentPerShard,
            minTime: minTimePerShard
        });
    }

    connect(retryDelay = 5000) {
        this.ws = new WebSocket(this.url, {
            headers: {
                Authorization: this.authToken
            }
        });

        this.ws.on('open', () => {
            console.log('🔗 Connected to Grow A Garden WebSocket');
        });

        this.ws.on('message', async (message) => {
            try {
                const formattedMessage = JSON.parse(message);

                this.sendNotification(formattedMessage.type, formattedMessage);
                
                if (formattedMessage.type === 'travelingmerchant_stock') {
                    const logChannel = await this.client.channels.fetch('1384218752765268040');
                    await logChannel.send(`📦 Received traveling merchant stock update:\n\n${JSON.stringify(formattedMessage, null, 2)}`);
                }
            } catch (err) {
                console.error('⚠️ Failed to parse WebSocket message:', err);
            }
        });

        this.ws.on('error', (err) => {
            console.error('⚠️ WebSocket error:', err);
        });

        this.ws.on('close', (code, reason) => {
            console.log(`❌ Disconnected from WebSocket. Code: ${code}, Reason: ${reason}`);
            console.log(`🔁 Attempting to reconnect in ${retryDelay / 1000} seconds...`);

            setTimeout(() => {
                this.connect(retryDelay);
            }, retryDelay);
        });
    }

    async handleSeedGearBuffer(type, data) {
        // Clear existing timeout if any
        if (seedGearBuffer.timeout) {
            clearTimeout(seedGearBuffer.timeout);
            seedGearBuffer.timeout = null;
        }

        // Store the data based on type
        if (type === 'seed_stock') {
            seedGearBuffer.seeds = data;
            console.log(`📦 Received seeds update: ${data.stock?.length || 0} items`);
        } else if (type === 'gear_stock') {
            seedGearBuffer.gears = data;
            console.log(`📦 Received gears update: ${data.stock?.length || 0} items`);
        }

        // Function to send combined notification
        const sendCombinedNotification = async () => {
            // Create combined data structure
            const combinedData = {
                stock: [
                    ...(seedGearBuffer.seeds?.stock || []),
                    ...(seedGearBuffer.gears?.stock || [])
                ]
            };

            console.log(`🚀 Sending combined seed/gear notification to all servers`);

            // Send using the seed_stock handler (which now handles both)
            await this.broadcastToAllServers('seed_stock', combinedData);

            // Reset the buffer
            seedGearBuffer.seeds = null;
            seedGearBuffer.gears = null;
        };

        // Check if we have both seeds and gears
        if (seedGearBuffer.seeds && seedGearBuffer.gears) {
            // We have both, send immediately
            console.log(`🚀 Both seeds and gears received, sending combined notification`);
            await sendCombinedNotification();
        } else {
            // We only have one type, set a timeout to wait for the other
            const hasSeeds = seedGearBuffer.seeds ? 'seeds' : '';
            const hasGears = seedGearBuffer.gears ? 'gears' : '';
            const waiting = hasSeeds ? 'gears' : 'seeds';
            console.log(`⏳ Buffering ${hasSeeds}${hasGears}, waiting ${BUFFER_TIMEOUT}ms for ${waiting}`);

            seedGearBuffer.timeout = setTimeout(async () => {
                // Timeout reached, send whatever we have
                console.log(`⏰ Timeout reached, sending buffered notification`);
                await sendCombinedNotification();
            }, BUFFER_TIMEOUT);
        }
    }

    async sendNotification(type, data) {
        if (!handlers[type]) {
            console.warn(`❌ No handler defined for type "${type}"`);
            return;
        }

        // Special handling for seed_stock and gear_stock to combine them
        if (type === 'seed_stock' || type === 'gear_stock') {
            return this.handleSeedGearBuffer(type, data);
        }

        // For all other types, send normally
        await this.broadcastToAllServers(type, data);
    }

    async broadcastToAllServers(type, data) {
        const startTime = Date.now();
        console.log(`🚀 Starting broadcast of "${type}" to all servers...`);

        this.client.shard.broadcastEval(
            async (client, context) => {
                const path = require('path');
                const StockNotification = require(path.resolve(context.paths.StockNotification));
                const RoleConfig = require(path.resolve(context.paths.RoleConfig));
                const AutoReactionConfig = require(path.resolve(context.paths.AutoReactionConfig));

                const configs = await StockNotification.find({});
                const handler = require(path.resolve(context.handlerPath));

                const Bottleneck = require('bottleneck');
                // Shard-aware rate limiting for webhook sending
                // Distribute 100 req/sec total across all shards to avoid IP rate limits
                const totalShards = client.shard?.count || 1;
                const requestsPerShard = Math.floor(100 / totalShards);
                const concurrentPerShard = Math.floor(50 / totalShards);
                const minTimePerShard = Math.ceil(1000 / requestsPerShard);

                const limiter = new Bottleneck({
                    reservoir: requestsPerShard,
                    reservoirRefreshAmount: requestsPerShard,
                    reservoirRefreshInterval: 1000,
                    maxConcurrent: concurrentPerShard,
                    minTime: minTimePerShard
                });

                // Pre-fetch all role configs and auto-reaction configs in parallel for maximum speed
                const roleConfigPromises = configs.map(config =>
                    RoleConfig.findOne({ guildId: config.guildId })
                );
                const autoReactionConfigPromises = configs.map(config =>
                    AutoReactionConfig.findOne({ guildId: config.guildId })
                );

                const [roleConfigs, autoReactionConfigs] = await Promise.all([
                    Promise.allSettled(roleConfigPromises),
                    Promise.allSettled(autoReactionConfigPromises)
                ]);

                // Create all notification tasks
                const notificationTasks = configs.map((config, index) => {
                    const guild = client.guilds.cache.get(config.guildId);
                    if (!guild) return null;

                    const roleConfigResult = roleConfigs[index];
                    const roleConfig = roleConfigResult.status === 'fulfilled' ? roleConfigResult.value : null;

                    const autoReactionConfigResult = autoReactionConfigs[index];
                    const autoReactionConfig = autoReactionConfigResult.status === 'fulfilled' ? autoReactionConfigResult.value : null;

                    return limiter.schedule(async () => {
                        try {
                            // Call the handler which will handle channel fetching and sending internally
                            await handler({ client, data: context.data, config, roleConfig, autoReactionConfig });
                            return config.guildId;
                        } catch (err) {
                            // More detailed error logging
                            if (err.code === 50013) {
                                console.warn(`⚠️ Missing permissions in guild ${config.guildId}`);
                            } else if (err.code === 10003) {
                                console.warn(`⚠️ Channel not found in guild ${config.guildId}`);
                            } else if (err.code === 50001) {
                                console.warn(`⚠️ Missing access in guild ${config.guildId}`);
                            } else {
                                console.warn(`⚠️ Failed to send to guild ${config.guildId}: ${err.message}`);
                            }
                            return null;
                        }
                    });
                }).filter(task => task !== null);

                // Execute all notifications in parallel with rate limiting
                const results = await Promise.allSettled(notificationTasks);
                const sentGuilds = results
                    .filter(result => result.status === 'fulfilled' && result.value)
                    .map(result => result.value);

                return sentGuilds;
            },
            {
                context: {
                    data,
                    handlerPath: path.join(__dirname, 'handlers', `${type}.js`),
                    paths: {
                        StockNotification: path.join(__dirname, '../models/StockNotification.js'),
                        RoleConfig: path.join(__dirname, '../models/roleConfig.js'),
                        AutoReactionConfig: path.join(__dirname, '../models/autoReactionConfig.js')
                    }
                }
            }
        )
        .then(results => {
            const total = results.flat().length;
            const duration = Date.now() - startTime;
            const rate = total > 0 ? (total / (duration / 1000)).toFixed(1) : '0';
            console.log(`📨 Sent "${type}" update to ${total} guild(s) in ${duration}ms (${rate} guilds/sec)`);
        })
        .catch(err => {
            console.error('❌ Broadcast error:', err);
        });
    }

    close() {
        if (this.ws) this.ws.close();
    }
}

module.exports = GardenSocket;