const { ApplicationCommandOptionType, MessageFlags, EmbedBuilder } = require('discord.js');
const fs = require('fs/promises');
const path = require('path');

module.exports = {
    data: {
        name: 'last-seen',
        description: 'Check when an item was last stocked',
        options: [
            {
                name: 'item',
                description: 'The item to check',
                type: ApplicationCommandOptionType.String,
                required: true,
                autocomplete: true
            }
        ]
    },

    run: async ({ interaction, client }) => {
        await interaction.deferReply();

        try {
            const itemID = interaction.options.getString('item');

            const apiResponse = await fetch(`https://api.joshlei.com/v2/growagarden/info/${itemID}`);
            const data = await apiResponse.json();

            let lastSeen
            if (data.last_seen === 0) {
                lastSeen = 'Never seen in stock';
            } else {
                lastSeen = `<t:${data.last_seen}:R>`;
            }

            const embed = new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle(`📦 Last Stocked | ${data.display_name}`)
                .addFields([
                    { name: 'Category', value: data.type.charAt(0).toUpperCase() + data.type.slice(1), inline: true },
                    { name: 'Rarity', value: data.rarity || 'N/A', inline: true },
                    { name: 'Last Seen', value: `${lastSeen}`, inline: false }
                ])
                .setThumbnail(data.icon)
                .setFooter({ text: `Requested by ${interaction.user.tag}` })
                .setTimestamp();

            interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.log("Error in lastStocked.js:", error);
            interaction.editReply({ content: 'Sorry, there was an error fetching the item data.', flags: MessageFlags.Ephemeral });
        }
    },

    autocomplete: async ({ interaction }) => {
        try {
            const response = await fetch('https://api.joshlei.com/v2/growagarden/info/');

            const itemsData = await response.json();
            const focusedValue = interaction.options.getFocused();

            const filtered = itemsData
                .filter(item => item.display_name.toLowerCase().includes(focusedValue.toLowerCase()))
                .map(item => ({ name: `${item.display_name} (${item.type})`, value: item.item_id }));

            await interaction.respond(filtered.slice(0, 25));
        } catch (error) {
            console.error('Error in autocomplete:', error);
            await interaction.respond([]);
        }
    },

    options: {
        deleted: false,
    },
};