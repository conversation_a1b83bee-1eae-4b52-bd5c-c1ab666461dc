const { WebhookClient } = require('discord.js');
const StockNotification = require('../models/StockNotification');

/**
 * Creates a webhook in the specified channel
 * @param {Channel} channel - Discord channel object
 * @param {string} name - Name for the webhook
 * @param {string} avatar - Avatar URL for the webhook (optional)
 * @returns {Promise<string|null>} Webhook URL or null if failed
 */
async function createWebhook(channel, name, avatar = null) {
    try {
        // Check if bot has permission to manage webhooks
        const botMember = channel.guild.members.me;
        const permissions = channel.permissionsFor(botMember);
        
        if (!permissions || !permissions.has('ManageWebhooks')) {
            console.warn(`⚠️ Missing ManageWebhooks permission in channel ${channel.id} (guild ${channel.guild.id})`);
            return null;
        }

        // Create the webhook with consistent branding
        const webhook = await channel.createWebhook({
            name: name,
            avatar: avatar || getWebhookAvatar(),
            reason: 'Garden Notifier stock notifications'
        });

        return webhook.url;
    } catch (error) {
        console.error(`❌ Failed to create webhook in channel ${channel.id}:`, error.message);
        return null;
    }
}

/**
 * Deletes a webhook by URL
 * @param {string} webhookUrl - The webhook URL to delete
 * @returns {Promise<boolean>} Success status
 */
async function deleteWebhook(webhookUrl) {
    try {
        if (!webhookUrl) return true;
        
        const webhook = new WebhookClient({ url: webhookUrl });
        await webhook.delete('Garden Notifier channel configuration changed');
        
        return true;
    } catch (error) {
        console.warn(`⚠️ Failed to delete webhook ${webhookUrl}:`, error.message);
        return false;
    }
}

/**
 * Sends a message via webhook and handles invalid webhooks
 * @param {string} webhookUrl - The webhook URL
 * @param {Object} messageData - Message data (content, embeds, etc.)
 * @param {string} guildId - Guild ID for database cleanup
 * @param {string} webhookType - Type of webhook (webhookUrl, eggWebhookUrl, etc.)
 * @returns {Promise<{success: boolean, message?: Message, invalidWebhook?: boolean}>} Result object
 */
async function sendWebhookMessage(webhookUrl, messageData, guildId = null, webhookType = null) {
    try {
        if (!webhookUrl) {
            return { success: false };
        }

        const webhook = new WebhookClient({ url: webhookUrl });
        const webhookMessage = await webhook.send(messageData);

        return { success: true, message: webhookMessage };
    } catch (error) {
        // Check if webhook is invalid/deleted
        const isInvalidWebhook = error.code === 10015 || // Unknown Webhook
                                error.code === 50027 || // Invalid Webhook Token
                                error.code === 10003;   // Unknown Channel (webhook channel deleted)

        if (isInvalidWebhook && guildId && webhookType) {
            // Remove invalid webhook from database
            await removeInvalidWebhook(guildId, webhookType);
            console.warn(`🗑️ Removed invalid ${webhookType} for guild ${guildId}`);
            return { success: false, invalidWebhook: true };
        }

        // Log other errors for debugging
        if (!isInvalidWebhook) {
            console.warn(`⚠️ Webhook send failed: ${error.message}`);
        }

        return { success: false, invalidWebhook: isInvalidWebhook };
    }
}

/**
 * Tests if a webhook URL is valid and accessible
 * @param {string} webhookUrl - The webhook URL to test
 * @returns {Promise<boolean>} Whether the webhook is valid
 */
async function testWebhook(webhookUrl) {
    try {
        if (!webhookUrl) return false;
        
        const webhook = new WebhookClient({ url: webhookUrl });
        await webhook.fetch();
        
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Gets webhook name (always "Garden Notifier")
 * @returns {string} Webhook name
 */
function getWebhookName() {
    return 'Garden Notifier';
}

/**
 * Gets webhook avatar URL
 * @returns {string} Avatar URL
 */
function getWebhookAvatar() {
    return 'https://i.imgur.com/QKXHv2v.png';
}

/**
 * Removes an invalid webhook from the database
 * @param {string} guildId - Guild ID
 * @param {string} webhookType - Type of webhook to remove
 * @returns {Promise<void>}
 */
async function removeInvalidWebhook(guildId, webhookType) {
    try {
        const updateData = {};
        updateData[webhookType] = null;

        await StockNotification.findOneAndUpdate(
            { guildId },
            { $unset: updateData }
        );
    } catch (error) {
        console.warn(`⚠️ Failed to remove invalid webhook for guild ${guildId}:`, error.message);
    }
}

module.exports = {
    createWebhook,
    deleteWebhook,
    sendWebhookMessage,
    testWebhook,
    getWebhookName,
    getWebhookAvatar,
    removeInvalidWebhook
};
