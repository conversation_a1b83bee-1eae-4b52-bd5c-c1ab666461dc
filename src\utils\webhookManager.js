const { WebhookClient } = require('discord.js');

/**
 * Creates a webhook in the specified channel
 * @param {Channel} channel - Discord channel object
 * @param {string} name - Name for the webhook
 * @param {string} avatar - Avatar URL for the webhook (optional)
 * @returns {Promise<string|null>} Webhook URL or null if failed
 */
async function createWebhook(channel, name, avatar = null) {
    try {
        // Check if bot has permission to manage webhooks
        const botMember = channel.guild.members.me;
        const permissions = channel.permissionsFor(botMember);
        
        if (!permissions || !permissions.has('ManageWebhooks')) {
            console.warn(`⚠️ Missing ManageWebhooks permission in channel ${channel.id} (guild ${channel.guild.id})`);
            return null;
        }

        // Create the webhook with consistent branding
        const webhook = await channel.createWebhook({
            name: name,
            avatar: avatar || getWebhookAvatar(),
            reason: 'Garden Notifier stock notifications'
        });

        return webhook.url;
    } catch (error) {
        console.error(`❌ Failed to create webhook in channel ${channel.id}:`, error.message);
        return null;
    }
}

/**
 * Deletes a webhook by URL
 * @param {string} webhookUrl - The webhook URL to delete
 * @returns {Promise<boolean>} Success status
 */
async function deleteWebhook(webhookUrl) {
    try {
        if (!webhookUrl) return true;
        
        const webhook = new WebhookClient({ url: webhookUrl });
        await webhook.delete('Garden Notifier channel configuration changed');
        
        return true;
    } catch (error) {
        console.warn(`⚠️ Failed to delete webhook ${webhookUrl}:`, error.message);
        return false;
    }
}

/**
 * Sends a message via webhook
 * @param {string} webhookUrl - The webhook URL
 * @param {Object} messageData - Message data (content, embeds, etc.)
 * @returns {Promise<Message|null>} Sent message or null if failed
 */
async function sendWebhookMessage(webhookUrl, messageData) {
    try {
        if (!webhookUrl) {
            return null;
        }

        const webhook = new WebhookClient({ url: webhookUrl });
        const webhookMessage = await webhook.send(messageData);

        return webhookMessage;
    } catch (error) {
        // Reduce logging overhead for better performance
        if (error.code !== 10015 && error.code !== 50027) { // Don't log common webhook errors
            console.warn(`⚠️ Webhook send failed: ${error.message}`);
        }
        return null;
    }
}

/**
 * Tests if a webhook URL is valid and accessible
 * @param {string} webhookUrl - The webhook URL to test
 * @returns {Promise<boolean>} Whether the webhook is valid
 */
async function testWebhook(webhookUrl) {
    try {
        if (!webhookUrl) return false;
        
        const webhook = new WebhookClient({ url: webhookUrl });
        await webhook.fetch();
        
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * Gets webhook name (always "Garden Notifier")
 * @returns {string} Webhook name
 */
function getWebhookName() {
    return 'Garden Notifier';
}

/**
 * Gets webhook avatar URL
 * @returns {string} Avatar URL
 */
function getWebhookAvatar() {
    return 'https://i.imgur.com/QKXHv2v.png';
}

module.exports = {
    createWebhook,
    deleteWebhook,
    sendWebhookMessage,
    testWebhook,
    getWebhookName,
    getWebhookAvatar
};
