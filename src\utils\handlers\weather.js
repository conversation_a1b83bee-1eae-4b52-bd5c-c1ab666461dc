const { EmbedBuilder } = require('discord.js');
const { sendWebhookMessage } = require('../webhookManager');
const path = require('path');
const fs = require('fs');

// Cache for weather data to avoid repeated file reads
let weatherDataCache = null;
let weatherDataCacheTime = 0;
const CACHE_DURATION = 60000; // 1 minute cache

// Helper function to get cached weather data
function getWeatherData() {
    const now = Date.now();
    if (!weatherDataCache || (now - weatherDataCacheTime) > CACHE_DURATION) {
        try {
            const weatherDataPath = path.join(__dirname, '../../data/weatherData.json');
            weatherDataCache = JSON.parse(fs.readFileSync(weatherDataPath, 'utf8'));
            weatherDataCacheTime = now;
        } catch (error) {
            console.warn('⚠️ Failed to load weatherData.json:', error.message);
            weatherDataCache = [];
        }
    }
    return weatherDataCache;
}

module.exports = async function handleWeather({ data, config, roleConfig }) {
    if (!data.stock || data.stock.length === 0) return false; // No weather data

    // Use webhook for sending
    const webhookUrl = config.weatherWebhookUrl;
    if (!webhookUrl) return false; // No webhook configured for weather

    // Get the weather data from the first item in the stock array
    const weatherData = data.stock[0];

    const weatherDataJSON = getWeatherData();
    const weather = weatherDataJSON.find(w => w.apiName === weatherData.weather);
    if (!weather) {
        console.warn(`Weather data not found for ${weatherData.weather}`);
        return false;
    }

    const weatherName = weather.name;
    const effects = weather.effects;

    const embed = new EmbedBuilder()
        .setTitle(`🌦️ Weather Event: ${weatherName}`)
        .setColor('#f1fbd2')
        .setTimestamp()
        .setDescription('A new weather event has started!')
        .addFields([
            { name: 'Duration', value: `${weatherData.duration} seconds`, inline: true },
            { name: 'Start', value: `<t:${weatherData.start_timestamp_unix}:R>`, inline: true },
            { name: 'End', value: `<t:${weatherData.end_timestamp_unix}:R>`, inline: true },
            { name: 'Effects', value: effects.join('\n'), inline: false }
        ])
        .setThumbnail(weatherData.image);

    // Add role ping if configured for this weather type
    let content = '';
    if (roleConfig?.weatherRoles && weatherData.weather) {
        const roleId = roleConfig.weatherRoles.get(weatherData.weather);
        if (roleId) {
            content = `<@&${roleId}>`;
        }
    }

    // Prepare message data
    const messageData = {
        content,
        embeds: [embed]
    };

    try {
        // Send via webhook
        const webhookMessage = await sendWebhookMessage(webhookUrl, messageData);
        if (!webhookMessage) {
            console.warn(`⚠️ Failed to send webhook message for guild ${config.guildId}, webhook may be invalid`);
            return false;
        }

        return true; // Success
    } catch (error) {
        console.warn(`⚠️ Failed to send weather notification in guild ${config.guildId}: ${error.message}`);
        return false;
    }
};